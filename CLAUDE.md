# Руководство по кодированию

===Данные для локальной разработке авторизации на проекте: имя: admin
пароль: qwe123
-Используй для повышение производительности там где это уместно Events/Listeners
-Не используй JS если это не просят у тебя, ты можешь существующий улучшать если надо, но САМ не создавай и не делай, все только через сервер делается и оптимизированно
-Старайся делать все оптимально производительно НО ВАЖНО ЧТОБЫ ВСЕ БЫЛО АКТУАЛЬНО КАК НАДО

- НИКАКИЕ ТЕСТЫ НЕ ДЕЛАЙ, ЛУЧШЕ ПРОСТО СРАЗУ ХОРОШО ВСЕ СДЕЛАТЬ!
-Важно чтобы структура проекта была компонентная и аккуратно все по папочкам и разделение было и легко масштабировалось все
- Адрес используй <http://127.0.0.1:8000/>
-В проекте код может быть плохо все организованно и запутанно, но ты пытайся понять, и находить проблемы даже не в предсказуемых местах
- Старайсяделать так чтобы в проекте был акцент на оптмизированный код и логику, так как важно что проект расчитан на большое кол-во пользователей в онлайн игре данной.
- Используй Tailwind НЕ ИСПОЛЬЗУЙ CSS вообще, только таилвинд
- #1a1814 — Фоновый цвет (основной)
Глубокий тёмный оттенок земли. Используется как базовый фон карточек, модалок и панелей. Не напрягает глаза.
-#2a2722 — Фоновый цвет (вторичный)
Чуть светлее основного. Идеален для разделения блоков или наведения выделения без яркости.
-#3b3629 — Цвет рамок и границ
Грубое "старое золото", создаёт эффект древности и структуры. Отлично контрастирует с фоном.
-#6e3f35 — Цвет акцентных рамок/кнопок
Красно-коричневый. Применяется для важных, агрессивных или горячих кнопок ("Покинуть", "Удалить").
-#59372d → #3c221b — Градиент опасной кнопки
Используется как фон кнопки, визуально усиливает важность действия. При наведении делает цвет глубже.
-#2f473c → #1e2e27 — Градиент безопасной кнопки
Умиротворяющий зелёно-темный градиент для нейтральных или положительных действий (например, "Остаться").
-#e4d7b0 — Заголовки
Цвет выцветшего пергамента. Яркий и контрастный к фону, подчёркивает важный текст.
-#d4cbb0 — Основной текст
Нейтральный и мягкий. Подходит для всего текста, удобно читается на тёмном фоне.
-#998d66 — Второстепенный текст
-Служебная информация, подписи, подсказки. Не отвлекает, но читаем.
-#c1a96e — Акценты и иконки
-Цвет золота. Для выделения важных фраз, иконок, цифр, эффектов.
rgba(193, 169, 110, 0.4) — Световое свечение
Применяется к иконкам и акцентам через drop-shadow или box-shadow, создаёт "магический эффект".

rgba(0, 0, 0, 0.5) — Тень текста
Делает шрифт читаемым и объёмным, особенно на светлом фоне.

# f8eac2 — Цвет текста кнопок
Мягкий светлый, похож на ткань или свет от факела. Контрастен и не режет глаз.

Градиенты → всегда снизу вверх (from → to)
Это создаёт впечатление "возвышения" — как будто кнопка светится от основания.
Тесты делай консольными командами, не над через тесты!
Контраст через материалы, а не яркость
В стиле dark fantasy лучше использовать глубину и текстуру, чем просто светлые цвета.
-Используй для удобства отдельно маршруты (но важно правильо применять все мидлвары из web в другие), так как web.php сильно перегружен множествами маршрутов

- В конце запроса твоего пиши руководства по командам = для тестов и.т.п
-Не забывай подлюкчать файлы в конфигурацию vite
- Никаких <script> внутри Blade-файлов.
JavaScript-код не должен быть вставлен прямо в шаблон — это нарушает структуру и делает код доступным в браузере.
✅ Вместо этого используйте отдельные JS-файлы.
Весь JavaScript размещается в папке resources/js/.
Для каждого логического блока — свой файл. (Или внутри под папки логические делай и там по смыслу js файлы)
- Использовать meta-тег для безопасной передачи токена
- Нельзя применять @apply к поведенческим классам (hover:, focus:, group-*, dark: и т.п.) — это вызывает ошибки сборки.
- НИКОГДА НЕ СБРАСЫВАЙ БД И НЕ ДЕЛАЙ ФРЕШ!!!
- НИВКОЕМ СЛУЧАИ НЕ СБРАСЫВАЙ ДАННЫЕ В БД ДАЖЕ ПРИ ТЕСТАХ!!!!!!
❌ НИКОГДА НЕ ИСПОЛЬЗУЙ :
RefreshDatabase - ПОЛНОСТЬЮ УДАЛЯЕТ ВСЮ БД
DatabaseMigrations - Запускает миграции, может повредить БД
DatabaseTransactions - Может конфликтовать с Octane
CreatesApplication с полной инициализацией БД
Любые команды миграций в тестах:
php artisan migrate:fresh
php artisan migrate:reset
php artisan migrate:rollback
php artisan db:wipe
php artisan schema:drop
