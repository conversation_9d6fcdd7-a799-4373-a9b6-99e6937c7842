<!DOCTYPE html>
<html lang="en">
@php
    use Illuminate\Support\Facades\Auth;
    $currentAuthUser = Auth::user();
    // Удаляем определение $currentAuthUserProfile, так как оно теперь приходит из контроллера
@endphp

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{-- CSRF-токен для безопасности AJAX-запросов --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Профиль: {{ $user->name }}</title>

    {{-- Подключаем стили и скрипты через Vite --}}
    @vite(['resources/css/app.css', 'resources/js/app.js', 'resources/css/experience-progress.css', 'resources/js/user/profile-other-interactions.js'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">
    {{-- Основной контейнер с единым стилем --}}
    <div class="container max-w-md mx-auto px-1 py-0 border-2 border-[#a6925e] rounded-lg flex-grow">

        {{-- HP/MP блок с уведомлениями (используем компонент) --}}
        {{-- Русский комментарий: Для профиля другого игрока используем actualResources текущего авторизованного
        пользователя для HP/MP бара --}}
        @php
            // Получаем актуальные ресурсы текущего авторизованного пользователя для HP/MP бара
            $currentUserActualResources = $currentAuthUserProfile ? $currentAuthUserProfile->getActualResources() : ['current_hp' => 0, 'current_mp' => 0];
        @endphp
        <x-layout.hp-mp-bar :actualResources="$currentUserActualResources" :userProfile="$currentAuthUserProfile">
            {{-- Слот для уведомлений между HP и MP --}}
            <x-layout.notifications-bar :hasUnreadMessages="$hasUnreadMessages ?? false"
                :unreadMessagesCount="$unreadMessagesCount ?? 0" :hasBrokenItems="$hasBrokenItems ?? false"
                :brokenItemsCount="$brokenItemsCount ?? 0" />
        </x-layout.hp-mp-bar>

        {{-- Отображение валюты с прогрессом опыта (текущего пользователя) --}}
        @php
            // Получаем объект профиля текущего пользователя для валют и опыта
            $authUserProfile = $currentAuthUserProfile;
            // Правильно рассчитываем прогресс через метод getExperienceProgress()
            $authExperienceProgress = $authUserProfile ? $authUserProfile->getExperienceProgress() : null;
        @endphp
        <x-layout.currency-display :userProfile="$currentAuthUserProfile"
            :experienceProgress="$authExperienceProgress" />

        {{-- Приветственное сообщение --}}
        <div class="text-center flex justify-center space-x-1 max-w-[300px] mx-auto">
            <x-battle.welcome-message />
        </div>

        {{-- Заголовок страницы с фоновым изображением (консистентно с локациями) --}}
        <div class="w-full mx-auto">
            <x-breadcrumbs :breadcrumbs="$breadcrumbs" />
            {{-- Декоративная HR-линия --}}
            <div class="px-4 py-0.5">
                <hr class="border-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-70">
            </div>
            {{-- Название локации с фоновым изображением --}}
            <x-layout.location-name title="Профиль игрока {{ $user->name }}" />
        </div>

        {{-- Основной контентный блок с очень темным фоном --}}
        <div class="bg-[#211f1a] px-2.5  shadow-inner"> {{-- Увеличены отступы --}}

            {{-- Блок персонажа (Имя, уровень, раса, класс) --}}
            <div class="text-center mb-2 relative"> {{-- Увеличен нижний отступ --}}

                {{-- ID игрока (неброско) --}}
                <div class="text-xs text-[#a09a8a] mb-1">ID: {{ $user->id }}</div>
                {{-- Имя игрока крупнее --}}
                <h1 class="text-2xl text-[#e5b769] font-bold  tracking-wide ">{{ $user->name }}</h1>
                {{-- Медальон уровня --}}
                <div
                    class="absolute -top-0 -right-2 w-10 h-10 rounded-full bg-gradient-to-b from-[#4a4a3d] to-[#2b2a21] border-2 border-[#a6925e] flex items-center justify-center shadow-md">
                    <span class="text-[#e5b769] text-sm font-bold">{{ $userProfile->level ?? 1 }}</span>
                </div>
                {{-- Раса и класс --}}
                <p class="text-[#d9d3b8] flex items-center justify-center gap-3 mt-1">
                    <span
                        class="px-2.5 py-0.5 bg-[#38352c] rounded-md border border-[#514b3c] text-sm shadow-sm flex items-center">
                        @if ($userProfile->race === 'lunarius')
                            <img src="{{ asset('assets/Racelunarius.png') }}" alt="Лунариус" class="w-5 h-5 mr-1">
                        @elseif ($userProfile->race === 'solarius')
                            <img src="{{ asset('assets/Racesolarius.png') }}" alt="Солариус" class="w-5 h-5 mr-1">
                        @endif
                        {{ $userProfile->race ?? 'Не указана' }}
                    </span>
                    <span
                        class="px-2.5 py-0.5 bg-[#38352c] rounded-md border border-[#514b3c] text-sm shadow-sm">{{ $userProfile->class ?? 'Не указан' }}</span>
                </p>
            </div>

            {{-- Блок Опыта --}}
            {{-- Русский комментарий: Используем улучшенный компонент для отображения прогресса опыта
            просматриваемого пользователя --}}
            <div class="mb-3">
                <x-layout.experience-progress-bar :experienceProgress="$experienceProgress ?? null" type="thick"
                    :showText="true" :showLevel="true" :isMaxLevel="isset($experienceProgress['is_max_level']) ? $experienceProgress['is_max_level'] : false" />
            </div>

            {{-- GS (Gear Score) --}}
            {{-- Более внушительный, но не кричащий блок GS --}}
            <div class="bg-[#2a271c] border border-[#514b3c] rounded-lg p-3 shadow-lg mb-2 relative">
                {{-- Контейнер
                GS --}}
                <div class="text-center relative flex items-center justify-center">

                    <div>
                        <span class="text-[#d9d3b8] text-sm uppercase tracking-wider block">Боевая мощь</span>
                        {{--
                        Заголовок GS --}}
                        {{-- Значение GS крупнее и золотое --}}
                        <div class="text-[#e5b769] text-2xl font-bold leading-tight">{{ $gs ?? 0 }}</div>
                        {{--
                        Значение GS --}}
                    </div>
                </div>
            </div>

            {{-- Основной блок экипировки --}}
            {{-- Блок с фоновым изображением для экипировки и аватарки --}}
            <div class="relative flex flex-col items-center mb-4" style="background-image: url('{{ asset('assets/bg/bg-items.png') }}'); background-size: cover; background-position: center; background-repeat: no-repeat; padding: 20px; border-radius: 8px;">
                {{-- Средняя часть --}}
                <div class="relative flex items-center justify-center mb-3">
                    {{-- Левая колонка слотов --}}
                    <div class="flex flex-col space-y-2.5 mr-3">
                        {{-- Слот шлема --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            @if (isset($equippedItems['шлем']))
                                {{-- Присваиваем переменные --}}
                                @php
                                    $gameItem = $equippedItems['шлем'];
                                    $item = $gameItem->item;
                                @endphp
                                <a
                                    href="{{ route('user.equipped-item.details.other', ['userId' => $user->id, 'gameItemId' => $gameItem->id]) }}">
                                    <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                        alt="{{ $item->name ?? 'Предмет' }}"
                                        class="w-11 h-11 object-cover transition-transform duration-200 group-hover:scale-110" />
                                </a>
                            @else
                                <img src="{{ asset('assets/helmetIcon.png') }}" alt="Пустой слот шлем"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 right-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                        {{-- Слот брони --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            @if (isset($equippedItems['тело']))
                                {{-- Присваиваем переменные --}}
                                @php
                                    $gameItem = $equippedItems['тело'];
                                    $item = $gameItem->item;
                                @endphp
                                <a
                                    href="{{ route('user.equipped-item.details.other', ['userId' => $user->id, 'gameItemId' => $gameItem->id]) }}">
                                    <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                        alt="{{ $item->name ?? 'Предмет' }}"
                                        class="w-11 h-11 object-cover transition-transform duration-200 group-hover:scale-110" />
                                </a>
                            @else
                                <img src="{{ asset('assets/armorIcon.png') }}" alt="Пустой слот броня"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 right-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                        {{-- Слот перчаток --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            @if (isset($equippedItems['перчатки']))
                                {{-- Присваиваем переменные --}}
                                @php
                                    $gameItem = $equippedItems['перчатки'];
                                    $item = $gameItem->item;
                                @endphp
                                <a
                                    href="{{ route('user.equipped-item.details.other', ['userId' => $user->id, 'gameItemId' => $gameItem->id]) }}">
                                    <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                        alt="{{ $item->name ?? 'Предмет' }}"
                                        class="w-11 h-11 object-cover transition-transform duration-200 group-hover:scale-110" />
                                </a>
                            @else
                                <img src="{{ asset('assets/glovesIcon.png') }}" alt="Пустой слот перчатки"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 right-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                        {{-- Слот обуви --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            @if (isset($equippedItems['обувь']))
                                {{-- Присваиваем переменные --}}
                                @php
                                    $gameItem = $equippedItems['обувь'];
                                    $item = $gameItem->item;
                                @endphp
                                <a
                                    href="{{ route('user.equipped-item.details.other', ['userId' => $user->id, 'gameItemId' => $gameItem->id]) }}">
                                    <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                        alt="{{ $item->name ?? 'Предмет' }}"
                                        class="w-11 h-11 object-cover transition-transform duration-200 group-hover:scale-110" />
                                </a>
                            @else
                                <img src="{{ asset('assets/bootsIcon.png') }}" alt="Пустой слот обувь"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 right-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                    </div>

                    {{-- Центральная картинка персонажа --}}
                    <div
                        class="w-44 h-64 bg-[#38352c] border-2 border-[#514b3c] rounded-lg flex items-center justify-center shadow-lg relative overflow-hidden">
                        {{-- ... декоративные уголки ... --}}
                        <div
                            class="absolute top-1 left-1 w-4 h-4 border-t-2 border-l-2 border-[#a6925e]/50 rounded-tl-md">
                        </div>
                        <div
                            class="absolute top-1 right-1 w-4 h-4 border-t-2 border-r-2 border-[#a6925e]/50 rounded-tr-md">
                        </div>
                        <div
                            class="absolute bottom-1 left-1 w-4 h-4 border-b-2 border-l-2 border-[#a6925e]/50 rounded-bl-md">
                        </div>
                        <div
                            class="absolute bottom-1 right-1 w-4 h-4 border-b-2 border-r-2 border-[#a6925e]/50 rounded-br-md">
                        </div>

                        {{-- Динамическое изображение персонажа на основе расы --}}
                        @php
                            $avatarPath = 'assets/character-placeholder.png'; // Заглушка по умолчанию
                            if ($userProfile->race === 'solarius') {
                                $avatarPath = 'assets/avatar/hero-solarius.jpg';
                            } elseif ($userProfile->race === 'lunarius') {
                                $avatarPath = 'assets/avatar/hero-lunarius.jpg';
                            }
                        @endphp
                        <img src="{{ asset($avatarPath) }}"
                            alt="Персонаж {{ getRaceDisplayName($userProfile->race ?? 'solarius') }}"
                            class="absolute inset-0 w-full h-full object-cover rounded-lg" />
                    </div>

                    {{-- Правая колонка слотов --}}
                    <div class="flex flex-col space-y-2.5 ml-3">
                        {{-- Слот браслета --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            @if (isset($equippedItems['браслет']))
                                {{-- Присваиваем переменные --}}
                                @php
                                    $gameItem = $equippedItems['браслет'];
                                    $item = $gameItem->item;
                                @endphp
                                <a
                                    href="{{ route('user.equipped-item.details.other', ['userId' => $user->id, 'gameItemId' => $gameItem->id]) }}">
                                    <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                        alt="{{ $item->name ?? 'Предмет' }}"
                                        class="w-11 h-11 object-cover transition-transform duration-200 group-hover:scale-110" />
                                </a>
                            @else
                                <img src="{{ asset('assets/bracerIcon.png') }}" alt="Пустой слот браслет"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                        {{-- Слот пояса --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            @if (isset($equippedItems['пояс']))
                                {{-- Присваиваем переменные --}}
                                @php
                                    $gameItem = $equippedItems['пояс'];
                                    $item = $gameItem->item;
                                @endphp
                                <a
                                    href="{{ route('user.equipped-item.details.other', ['userId' => $user->id, 'gameItemId' => $gameItem->id]) }}">
                                    <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                        alt="{{ $item->name ?? 'Предмет' }}"
                                        class="w-11 h-11 object-cover transition-transform duration-200 group-hover:scale-110" />
                                </a>
                            @else
                                <img src="{{ asset('assets/beltIcon.png') }}" alt="Пустой слот пояс"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                        {{-- Слот оружия --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            @if (isset($equippedItems['оружие']))
                                {{-- Присваиваем переменные --}}
                                @php
                                    $gameItem = $equippedItems['оружие'];
                                    $item = $gameItem->item;
                                @endphp
                                <a
                                    href="{{ route('user.equipped-item.details.other', ['userId' => $user->id, 'gameItemId' => $gameItem->id]) }}">
                                    <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                        alt="{{ $item->name ?? 'Предмет' }}"
                                        class="w-11 h-11 object-cover transition-transform duration-200 group-hover:scale-110" />
                                </a>
                            @else
                                <img src="{{ asset('assets/weaponIcon.png') }}" alt="Пустой слот оружие"
                                    class="w-11 h-11 object-cover opacity-45" />
                                <div
                                    class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                        {{-- Слот доп. оружия --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            @if (isset($equippedItems['доп оружие']))
                                {{-- Присваиваем переменные --}}
                                @php
                                    $gameItem = $equippedItems['доп оружие'];
                                    $item = $gameItem->item;
                                @endphp
                                <a
                                    href="{{ route('user.equipped-item.details.other', ['userId' => $user->id, 'gameItemId' => $gameItem->id]) }}">
                                    <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                        alt="{{ $item->name ?? 'Предмет' }}"
                                        class="w-11 h-11 object-cover transition-transform duration-200 group-hover:scale-110" />
                                </a>
                            @else
                                <img src="{{ asset('assets/weapon2Icon.png') }}" alt="Пустой слот доп. оружие"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                {{-- Нижние иконки (кольца и амулет) --}}
                <div class="grid grid-cols-3 gap-3 ">
                    {{-- Слот кольцо 1 --}}
                    <div
                        class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group overflow-hidden transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                        @if (isset($equippedItems['кольцо 1']))
                            {{-- Присваиваем переменные --}}
                            @php
                                $gameItem = $equippedItems['кольцо 1'];
                                $item = $gameItem->item;
                            @endphp
                            <a
                                href="{{ route('user.equipped-item.details.other', ['userId' => $user->id, 'gameItemId' => $gameItem->id]) }}">
                                <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                    alt="{{ $item->name ?? 'Предмет' }}"
                                    class="w-11 h-11 object-cover transform hover:scale-110 transition-transform duration-300" />
                            </a>
                        @else
                            <img src="{{ asset('assets/ringIcon.png') }}" alt="Пустой слот кольцо 1"
                                class="w-11 h-11 object-cover opacity-30" />
                            <div
                                class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                <span class="text-[#8c784e] text-xs">+</span>
                            </div>
                        @endif
                        <div
                            class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50">
                        </div>
                    </div>
                    {{-- Слот кольцо 2 --}}
                    <div
                        class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group overflow-hidden transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                        @if (isset($equippedItems['кольцо 2']))
                            {{-- Присваиваем переменные --}}
                            @php
                                $gameItem = $equippedItems['кольцо 2'];
                                $item = $gameItem->item;
                            @endphp
                            <a
                                href="{{ route('user.equipped-item.details.other', ['userId' => $user->id, 'gameItemId' => $gameItem->id]) }}">
                                <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                    alt="{{ $item->name ?? 'Предмет' }}"
                                    class="w-11 h-11 object-cover transform hover:scale-110 transition-transform duration-300" />
                            </a>
                        @else
                            <img src="{{ asset('assets/ringIcon.png') }}" alt="Пустой слот кольцо 2"
                                class="w-11 h-11 object-cover opacity-30" />
                            <div
                                class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                <span class="text-[#8c784e] text-xs">+</span>
                            </div>
                        @endif
                        <div
                            class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50">
                        </div>
                    </div>
                    {{-- Слот амулета --}}
                    <div
                        class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group overflow-hidden transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                        @if (isset($equippedItems['амулет']))
                            {{-- Присваиваем переменные --}}
                            @php
                                $gameItem = $equippedItems['амулет'];
                                $item = $gameItem->item;
                            @endphp
                            <a
                                href="{{ route('user.equipped-item.details.other', ['userId' => $user->id, 'gameItemId' => $gameItem->id]) }}">
                                <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                    alt="{{ $item->name ?? 'Предмет' }}"
                                    class="w-11 h-11 object-cover transform hover:scale-110 transition-transform duration-300" />
                            </a>
                        @else
                            <img src="{{ asset('assets/amuletIcon.png') }}" alt="Пустой слот амулет"
                                class="w-11 h-11 object-cover opacity-30" />
                            <div
                                class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                <span class="text-[#8c784e] text-xs">+</span>
                            </div>
                        @endif
                        <div
                            class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50">
                        </div>
                    </div>
                </div>
                {{-- ... остальная часть блока экипировки (умения, кнопки) ... --}}
                {{-- Умения персонажа --}}
                <div class="mt-5 w-full">
                    <div class="relative mb-3">
                        <div
                            class="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-[#2a271c] px-3 py-1 border border-[#a6925e] rounded-full z-10 shadow-md">
                            <h3 class="text-[#e5b769] text-base font-semibold">Умения</h3>
                        </div>
                    </div>
                    <div
                        class="grid grid-cols-4 gap-5 bg-[#2a271c] border border-[#514b3c] rounded-lg p-3 shadow-lg mt-2 mx-auto max-w-xs">
                        @for ($i = 0; $i < 4; $i++)
                            <div
                                class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group overflow-hidden transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                                <div
                                    class="w-11 h-11 bg-gradient-to-br from-[#4a4a3d] to-[#3b3a33] flex items-center justify-center rounded-md">
                                    <span class="text-[#8c784e] text-2xl">?</span>
                                </div>
                                <div
                                    class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-30">
                                </div>
                            </div>
                        @endfor
                    </div>
                </div>

                {{-- Панель быстрого доступа --}}
                <div class="flex items-center justify-center space-x-3 mt-2.5">
                    <button
                        class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg transition-all duration-300 group relative hover:shadow-lg hover:shadow-[#a6925e]/30">
                        <img src="{{ asset('assets/iconBook.png') }}" alt="умение"
                            class="w-9 h-9 transform group-hover:scale-110 transition-transform duration-300">

                    </button>
                    <a href="{{ route('user.equipment.other', ['userId' => $user->id]) }}"
                        class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg transition-all duration-300 group relative hover:shadow-lg hover:shadow-[#a6925e]/30">
                        <img src="{{ asset('assets/test1.png') }}" alt="снаряжение"
                            class="w-9 h-9 transform group-hover:scale-110 transition-transform duration-300">

                    </a>
                    <a href="javascript:void(0)" id="sendMessageBtn" data-user-id="{{ $user->id }}"
                        class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg transition-all duration-300 group relative hover:shadow-lg hover:shadow-[#a6925e]/30">
                        <span class="text-[#e5b769] text-2xl">📩</span>
                    </a>
                </div>
            </div>



            {{-- Характеристики персонажа --}}
            <div class="  rounded-lg  mb-6 shadow-lg relative overflow-hidden">

                {{-- Одна колонка для характеристик --}}
                <div class="grid grid-cols-1 gap-3">
                    {{-- ОСНОВНЫЕ ХАРАКТЕРИСТИКИ --}}
                    <div class="bg-[#2a271c] bg-opacity-90 rounded-lg p-3 border border-[#3d3828]">

                        <div class="space-y-1">
                            <div class="flex items-center">

                                <div class="flex-1"><span class="text-base text-[#d9d3b8]">Сила: </span><span
                                        class="text-base text-[#ffc83d] font-medium">{{ $effectiveStats['strength'] ?? 0 }}</span>
                                </div>
                            </div>
                            <div class="flex items-center">

                                <div class="flex-1"><span class="text-base text-[#d9d3b8]">Интеллект: </span><span
                                        class="text-base text-[#ffc83d] font-medium">{{ $effectiveStats['intelligence'] ?? 0 }}</span>
                                </div>
                            </div>
                            <div class="flex items-center">

                                <div class="flex-1"><span class="text-base text-[#d9d3b8]">Броня: </span><span
                                        class="text-base text-[#ffc83d] font-medium">{{ round($effectiveStats['armor'] ?? 0) }}</span>
                                </div>
                            </div>
                            <div class="flex items-center">

                                <div class="flex-1"><span class="text-base text-[#d9d3b8]">Восстановление:
                                    </span><span
                                        class="text-base text-[#ffc83d] font-medium">{{ $effectiveStats['recovery'] ?? 0 }}</span>
                                </div>
                            </div>
                            <div class="flex items-center">

                                <div class="flex-1">
                                    <span class="text-base text-[#d9d3b8]">Шанс крита: </span>
                                    <span
                                        class="text-base text-[#ffc83d] font-medium">{{ $effectiveStats['crit_chance'] ?? 0 }}</span>
                                    {{-- Отображаем числовое значение стата --}}
                                    <span
                                        class="text-base text-green-600 ">({{ $userProfile->getFormattedCritChance() }})</span>
                                    {{-- Отображаем рассчитанный процент в скобках --}}
                                </div>
                            </div>
                            <div class="flex items-center">

                                <div class="flex-1">
                                    <span class="text-base text-[#d9d3b8]">Сила крита: </span>
                                    <span
                                        class="text-base text-[#ffc83d] font-medium">{{ $effectiveStats['crit_damage'] ?? 0 }}</span>
                                    {{-- Отображаем числовое значение стата --}}
                                    <span
                                        class="text-base text-green-600 ">({{ $userProfile->getFormattedCritDamage() }})</span>
                                    {{-- Отображаем рассчитанный множитель в скобках --}}
                                </div>
                            </div>
                        </div>
                    </div>
                    {{-- РЕСУРСЫ (HP/MP) и АКТИВНЫЕ ЭФФЕКТЫ --}}
                    <div class="bg-[#2a271c] bg-opacity-90 rounded-lg p-1.5 border border-[#3d3828]">

                        <div class="space-y-1">
                            <div class="flex items-center">
                                <img src="{{ asset('assets/user/hpIcon.png') }}" alt="HP" class="w-6 h-6 mr-2.5">
                                <div class="flex-1"><span class="text-base text-[#d9d3b8]">HP: </span><span
                                        class="text-base text-[#ffc83d] font-medium">{{ $viewedUserActualResources['current_hp'] ?? 0 }}
                                        /
                                        {{ $userProfile->max_hp ?? '?' }}</span></div>
                            </div>
                            <div class="flex items-center">
                                <img src="{{ asset('assets/user/mp-icon.png') }}" alt="MP" class="w-6 h-6 mr-2.5">
                                <div class="flex-1"><span class="text-base text-[#d9d3b8]">MP: </span><span
                                        class="text-base text-[#ffc83d] font-medium">{{ $viewedUserActualResources['current_mp'] ?? 0 }}
                                        /
                                        {{ $userProfile->max_mp ?? '?' }}</span></div>
                            </div>
                        </div>
                        {{-- Активные эффекты --}}
                        <div class="mt-2">
                            <div class="text-[#d9d3b8] text-base font-medium">Активные эффекты</div>
                            <div class="flex flex-wrap gap-2.5 p-2.5 bg-[#191815] rounded border border-[#3d3828]">
                                @if($currentlyActiveEffects->count() > 0)
                                    {{-- Русский комментарий: Отображаем реальные активные эффекты просматриваемого игрока
                                    --}}
                                    @foreach($currentlyActiveEffects as $effect)
                                        <div class="flex flex-col items-center text-center"
                                            title="{{ $effect->skill->name ?? 'Неизвестный эффект' }} ({{ $effect->remaining_duration }} сек.)">
                                            <div class="relative">
                                                <div
                                                    class="w-9 h-9 rounded-full bg-[#222114] border border-[#a6925e] flex items-center justify-center overflow-hidden">
                                                    {{-- Русский комментарий: Используем иконку эффекта или заглушку --}}
                                                    @php
                                                        // Получаем путь к иконке эффекта или используем заглушку
                                                        $effectIcon = $effect->skill->icon_path ?? asset('assets/skillLightOfFaith.png');
                                                    @endphp
                                                    <img src="{{ $effectIcon }}"
                                                        alt="{{ $effect->skill->name ?? 'Неизвестный эффект' }}"
                                                        class="w-7 h-7 object-cover rounded-full">
                                                </div>
                                                @if($effect->stacks > 1)
                                                    <div
                                                        class="absolute -top-1 -right-1 w-4 h-4 bg-[#222114] rounded-full flex items-center justify-center border border-[#a6925e]">
                                                        <span class="text-[#e5b769] text-[9px]">{{ $effect->stacks }}</span>
                                                    </div>
                                                @endif
                                            </div>
                                            {{-- Русский комментарий: Отображаем время в секундах под иконкой --}}
                                            <span class="text-[#e5b769] text-xs mt-1 font-medium">
                                                {{ $effect->remaining_duration }}с
                                            </span>
                                        </div>
                                    @endforeach
                                @else
                                    {{-- Русский комментарий: Заглушка когда нет активных эффектов --}}
                                    <div class="w-full text-center text-[#8c784e] text-sm py-2">
                                        Нет активных эффектов
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            {{-- Статистика Боев --}}
            <div class="bg-[#211f1a] border border-[#a6925e] rounded-lg p-4 shadow-lg  relative overflow-hidden">
                <div
                    class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-50">
                </div>

                <div class="grid grid-cols-2 gap-4">
                    {{-- PvP статистика --}}
                    <div class="bg-[#2b2a21] border border-[#514b3c] rounded-lg p-3.5 shadow-sm">
                        <div class="text-center font-semibold text-[#e5b769] mb-2 text-base">PvP</div>
                        <div class="flex justify-between items-center text-[#d9d3b8] text-sm mb-1">
                            <span>Победы:</span>
                            <span class="text-[#a6e55e] font-medium">{{ $userStatistics->pvp_wins ?? 0 }}</span>
                        </div>
                        <div class="flex justify-between items-center text-[#d9d3b8] text-sm">
                            <span>Поражения:</span>
                            <span class="text-[#e55e5e] font-medium">{{ $userStatistics->pvp_losses ?? 0 }}</span>
                        </div>
                    </div>
                    {{-- PvE статистика --}}
                    <div class="bg-[#2b2a21] border border-[#514b3c] rounded-lg p-3.5 shadow-sm">
                        <div class="text-center font-semibold text-[#e5b769] mb-2 text-base">PvE</div>
                        <div class="flex justify-between items-center text-[#d9d3b8] text-sm mb-1">
                            <span>Победы:</span>
                            <span class="text-[#a6e55e] font-medium">{{ $userStatistics->pve_wins ?? 0 }}</span>
                        </div>
                        <div class="flex justify-between items-center text-[#d9d3b8] text-sm">
                            <span>Поражения:</span>
                            <span class="text-[#e55e5e] font-medium">{{ $userStatistics->pve_losses ?? 0 }}</span>
                        </div>
                    </div>
                </div>
                <div
                    class="absolute bottom-0 right-0 w-full h-1 bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-50">
                </div>
            </div>

            {{-- Общая информация --}}
            {{-- Общая информация --}}
            {{-- ИЗМЕНЕНО: Удален главный заголовок, удален блок опыта, удалены отступы/иконки у строк --}}
            <div class="bg-[#211f1a] border border-[#a6925e] rounded-lg p-4 shadow-lg relative overflow-hidden mb-4">
                {{-- Верхняя декоративная линия --}}
                <div
                    class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-50">
                </div>
                {{-- ИЗМЕНЕНО: Заголовок блока удален --}}

                {{-- ИЗМЕНЕНО: Шкала опыта удалена --}}

                {{-- Остальная информация --}}
                <div class="text-[#d9d3b8] text-base"> {{-- ИЗМЕНЕНО: Удален класс space-y-3 --}}

                    {{-- Статус онлайн/оффлайн --}}
                    {{-- ИЗМЕНЕНО: Точке добавлен отрицательный отступ -ml-4 и увеличен правый отступ mr-2 для
                    компенсации --}}
                    <div class="flex items-center"> {{-- Убедимся, что нет relative --}}
                        {{-- Индикатор статуса (точка) --}}
                        {{-- Смещен влево на величину p-4 блока, добавлен правый отступ --}}
                        {{-- ИЗМЕНЕНО: Условие проверки теперь использует переменную $userStatus --}}
                        <span class="-ml-3 mr-0.5 leading-none
                                @if ($userStatus === 'online') text-green-500 animate-pulse {{-- Зеленая точка для онлайна --}}
                                @elseif ($userStatus === 'idle') text-gray-500 {{-- Серая точка для неактивных --}}
                                @else text-red-500 {{-- Красная точка для оффлайна --}} @endif
                            ">●</span>
                        {{-- Контейнер для текста статуса (выровнен с остальными строками) --}}
                        <div class="font-medium">
                            {{-- ИЗМЕНЕНО: Условие также использует $userStatus --}}
                            @if ($userStatus === 'online')
                                {{-- Текст для онлайна (берем из $displayLocation, она должна содержать локацию) --}}
                                <span class="text-green-400">{{ $displayLocation ?? 'Онлайн' }}</span>
                            @elseif ($userStatus === 'idle')
                                {{-- Текст для неактивного пользователя --}}
                                <span class="text-gray-400">{{ $displayLocation ?? 'Неактивен' }}</span>
                            @else
                                {{-- Текст для оффлайна (просто выводим $displayLocation, она содержит "Не в сети
                                (время)") --}}
                                <span class="text-red-500">{{ $displayLocation ?? 'Не в сети' }}</span>
                            @endif
                        </div>
                    </div>

                    {{-- Профессия --}}
                    <div class="flex items-center"> {{-- ИЗМЕНЕНО: Удален класс space-x-2.5 --}}
                        {{-- ИЗМЕНЕНО: Иконка профессии удалена --}}
                        {{-- Название профессии --}}
                        <div class="font-medium">Профессия: <span
                                class="text-[#e5b769]">{{ $userProfile->profession ?? 'Нет' }}</span></div>
                    </div>

                    {{-- Время в игре --}}
                    <div class="flex items-center"> {{-- ИЗМЕНЕНО: Удален класс space-x-2.5 --}}
                        {{-- ИЗМЕНЕНО: Иконка времени удалена --}}
                        <div class="font-medium">
                            Провел времени: <span class="text-[#e5b769]">
                                {{-- Форматированное время в игре (часы, минуты) --}}
                                {{ $userStatistics->hours_played ?? 0 }}ч
                                {{ $userStatistics->minutes_played ?? 0 }}м
                                {{-- Можно добавить секунды, если нужно: $userStatistics->seconds_accumulated ?? 0
                                }}с --}}
                            </span>
                        </div>
                    </div>
                    {{-- Дата регистрации --}}
                    <div class="flex items-center"> {{-- ИЗМЕНЕНО: Удален класс space-x-2.5 --}}
                        {{-- ИЗМЕНЕНО: Иконка календаря удалена --}}
                        <div class="font-medium">
                            В игре с: <span class="text-[#e5b769]">
                                {{-- Форматированная дата регистрации --}}
                                {{ $user->formatted_date ?? $user->created_at->format('d.m.Y') }}
                            </span>
                        </div>
                    </div>
                </div>
                {{-- Нижняя декоративная линия --}}
                <div
                    class="absolute bottom-0 right-0 w-full h-1 bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-50">
                </div>
            </div>
        </div> {{-- Закрытие основного контентного блока --}}
    </div> {{-- Закрываем контейнер страницы --}}

    {{-- Нижние кнопки навигации --}}
    <x-layout.navigation-buttons />

    {{-- Футер --}}
    <x-layout.footer :onlineCount="$onlineCount ?? 0" />

</body>

</html>