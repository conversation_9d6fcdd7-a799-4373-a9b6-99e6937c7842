<!DOCTYPE html>
<html lang="en">
@php use Illuminate\Support\Facades\Auth; @endphp

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{-- CSRF-токен для безопасности AJAX-запросов --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Персонаж: {{ Auth::user()->name }}</title>

    {{-- Подключаем стили и скрипты через Vite --}}
    @vite(['resources/css/app.css', 'resources/js/app.js', 'resources/css/user-profile.css', 'resources/css/experience-progress.css', 'resources/js/user/profile-interactions.js'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">
    {{-- О<PERSON>н<PERSON>ной контейнер с единым стилем --}}
    <div class="container max-w-md mx-auto px-1 py-0 border-2 border-[#a6925e] rounded-lg flex-grow">

        {{-- HP/MP блок с уведомлениями (используем компонент) --}}
        <x-layout.hp-mp-bar :actualResources="$actualResources" :userProfile="$userProfile">
            {{-- Слот для уведомлений между HP и MP --}}
            <x-layout.notifications-bar :hasUnreadMessages="$hasUnreadMessages ?? false"
                :unreadMessagesCount="$unreadMessagesCount ?? 0" :hasBrokenItems="$hasBrokenItems ?? false"
                :brokenItemsCount="$brokenItemsCount ?? 0" />
        </x-layout.hp-mp-bar>

        {{-- Отображение валюты (используем компонент) --}}
        <x-layout.currency-display :userProfile="$userProfile" :experienceProgress="$experienceProgress ?? null" />

        {{-- Уведомление о приглашении в гильдию --}}
        @if(Auth::check())
            @php
                $guildInvitation = Auth::user()->getLatestGuildInvitation();
            @endphp
            <x-layout.guild-invitation :guildInvitation="$guildInvitation" />
        @endif

        {{-- Заголовок страницы с фоновым изображением (консистентно с локациями) --}}
        <div class="w-full mx-auto">
            <x-breadcrumbs :breadcrumbs="$breadcrumbs" />
            {{-- Декоративная HR-линия --}}
            <div class="px-4 py-0.5">
                <hr class="border-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-70">
            </div>
            {{-- Название локации с фоновым изображением --}}
            <x-layout.location-name title="Персонаж" />
        </div>

        {{-- Проверяем переменные для возврата в бой --}}
        @if (isset($canReturnToBattle) && $canReturnToBattle && isset($lastBattleRoute) && $lastBattleRoute)
            <div class="mb-2">
                <a href="{{ url($lastBattleRoute) }}"
                    class="block w-full max-w-xs mx-auto px-2 py-2 text-lg font-semibold text-[#c4a46b]
                                                                                                                                                                   bg-gradient-to-b from-[#2b2a21] to-[#1d1c17] border border-[#8c784e]
                                                                                                                                                                   rounded-md uppercase tracking-wide text-center
                                                                                                                                                                   hover:border-[#b59e70] hover:text-[#dfc590] hover:shadow-md
                                                                                                                                                                   transition-all duration-300">
                    ⚔️ ВЕРНУТЬСЯ В БОЙ ⚔️
                </a>
            </div>
        @endif

        {{-- Основной контентный блок (используем компонент) --}}
        <x-layout.content-block>

            {{-- Блок персонажа (Имя, уровень, раса, класс) --}}
            {{-- Классы идентичны profileOther --}}
            <div class="text-center mb-2 relative"> {{-- Увеличен нижний отступ --}}

                {{-- ID игрока (неброско) --}}
                <div class="text-xs text-[#a09a8a] mb-1">ID: {{ $user->id }}</div>
                {{-- Имя игрока (крупнее) --}}
                <h1 class="text-2xl text-[#e5b769] font-bold  tracking-wide ">{{ $user->name }}</h1>
                {{-- Медальон уровня (как в profileOther) --}}
                <div
                    class="absolute -top-0 -right-2 w-10 h-10 rounded-full bg-gradient-to-b from-[#4a4a3d] to-[#2b2a21] border-2 border-[#a6925e] flex items-center justify-center shadow-md">
                    <span class="text-[#e5b769] text-sm font-bold">{{ $userProfile->level ?? 1 }}</span>
                    {{-- Уровень --}}
                </div>
                {{-- Раса и класс (стили из profileOther) --}}
                <p class="text-[#d9d3b8] flex items-center justify-center gap-3 mt-1">
                    <span
                        class="px-2.5 py-0.5 bg-[#38352c] rounded-md border border-[#514b3c] text-sm shadow-sm flex items-center">
                        @if ($userProfile->race === 'lunarius')
                            <img src="{{ asset('assets/race/Racelunarius.png') }}" alt="Лунариус" class="w-5 h-5">
                        @elseif ($userProfile->race === 'solarius')
                            <img src="{{ asset('assets/race/Racesolarius.png') }}" alt="Солариус" class="w-5 h-5">
                        @endif
                    </span>
                    <span
                        class="px-2.5 py-0.5 bg-[#38352c] rounded-md border border-[#514b3c] text-sm shadow-sm">{{ $userProfile->class ?? 'Не указан' }}</span>
                    {{-- Класс --}}
                </p>
            </div>

            {{-- Блок Опыта --}}
            {{-- Русский комментарий: Используем улучшенный компонент для отображения прогресса опыта --}}
            <div class="mb-3">
                <x-layout.experience-progress-bar :experienceProgress="$experienceProgress ?? null" type="thick"
                    :showText="true" :showLevel="true" :isMaxLevel="isset($experienceProgress['is_max_level']) ? $experienceProgress['is_max_level'] : false" />
            </div>

            {{-- GS (Gear Score) --}}
            {{-- Классы идентичны profileOther --}}
            <div class="bg-[#2a271c] border border-[#514b3c] rounded-lg p-3 shadow-lg mb-2 relative">
                {{-- Контейнер GS --}}
                <div class="text-center relative flex items-center justify-center">

                    <div>
                        <span class="text-[#d9d3b8] text-sm uppercase tracking-wider block">Боевая мощь</span>
                        {{-- Заголовок GS --}}
                        {{-- Значение GS крупнее и золотое --}}
                        <div class="text-[#e5b769] text-2xl font-bold leading-tight">{{ $gs ?? 0 }}</div>
                        {{-- Значение GS --}}
                    </div>
                </div>
            </div>

            {{-- Русский комментарий: Верхний блок активных эффектов удален, так как дублируется с нижним блоком --}}





            {{-- Основной блок экипировки --}}
            {{-- Классы идентичны profileOther (удалены всплывающие подсказки и mt-4 у нижних слотов) --}}
            {{-- Блок с фоновым изображением для экипировки и аватарки --}}
            <div class="relative flex flex-col items-center mb-4" style="background-image: url('{{ asset('assets/bg/bg-items.png') }}'); background-size: cover; background-position: center; background-repeat: no-repeat; padding: 20px; border-radius: 8px;">
                {{-- Средняя часть: слоты и картинка --}}
                <div class="relative flex items-center justify-center mb-3">
                    {{-- Левая колонка слотов --}}
                    <div class="flex flex-col space-y-2.5 mr-1">
                        {{-- Слот шлема --}}
                        {{-- Контейнер слота с градиентом, рамкой, тенью и эффектами при наведении --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">


                            @if (isset($equippedItems['шлем']))
                                @php
                                    $gameItem = $equippedItems['шлем']; // Экземпляр GameItem
                                    $item = $gameItem->item; // Связанная модель Item
                                    
                                    // Русский комментарий: Определение цвета качества предмета для свечения
                                    $qualityColors = [
                                        'Обычное' => 'glow-color-gray-200',
                                        'Необычное' => 'glow-color-green-400',
                                        'Редкое' => 'glow-color-blue-400',
                                        'Эпическое' => 'glow-color-purple-400',
                                        'Легендарное' => 'glow-color-orange-400',
                                        'Мифическое' => 'glow-color-red-500',
                                        'Божественное' => 'glow-color-pink-400',
                                    ];
                                    $glowColorClass = $qualityColors[$item->quality ?? 'Обычное'] ?? 'glow-color-gray-200';
                                @endphp
                                @if ($item)
                                    {{-- Если предмет экипирован и связь item существует --}}
                                    {{-- Контейнер с анимацией свечения --}}
                                    <div class="animate-breathing-glow {{ $glowColorClass }} w-11 h-11 rounded">
                                        {{-- Ссылка на детали предмета в инвентаре (используем правильный маршрут) --}}
                                        <a href="{{ route('user.equipped-item.details', ['gameItem' => $gameItem->id]) }}"
                                            class="block w-full h-full"> {{-- Используем 'gameItem' как имя параметра в маршруте --}}
                                            {{-- Иконка предмета с эффектом увеличения при наведении --}}
                                            <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                                alt="{{ $item->name ?? 'Предмет' }}"
                                                class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110 rounded" />
                                        </a>
                                    </div>
                                    {{-- ИЗМЕНЕНО: Всплывающая подсказка удалена --}}
                                @else
                                    {{-- Если предмет есть, но связь item отсутствует --}}
                                    <img src="{{ asset('assets/helmetIcon.png') }}" alt="Ошибка загрузки предмета"
                                        class="w-11 h-11 object-cover opacity-50" />
                                @endif
                            @else
                                {{-- Если слот пуст - иконка-заглушка --}}
                                <img src="{{ asset('assets/helmetIcon.png') }}" alt="Пустой слот шлем"
                                    class="w-11 h-11 object-cover opacity-30" />
                                {{-- Индикатор пустого слота (+) --}}
                                <div
                                    class="absolute top-0 right-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                        {{-- Слот брони (аналогично шлему) --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            @if (isset($equippedItems['тело']))
                                @php
                                    $gameItem = $equippedItems['тело'];
                                    $item = $gameItem->item;
                                    
                                    // Русский комментарий: Определение цвета качества предмета для свечения
                                    $qualityColors = [
                                        'Обычное' => 'glow-color-gray-200',
                                        'Необычное' => 'glow-color-green-400',
                                        'Редкое' => 'glow-color-blue-400',
                                        'Эпическое' => 'glow-color-purple-400',
                                        'Легендарное' => 'glow-color-orange-400',
                                        'Мифическое' => 'glow-color-red-500',
                                        'Божественное' => 'glow-color-pink-400',
                                    ];
                                    $glowColorClass = $qualityColors[$item->quality ?? 'Обычное'] ?? 'glow-color-gray-200';
                                @endphp
                                @if ($item)
                                    {{-- Контейнер с анимацией свечения --}}
                                    <div class="animate-breathing-glow {{ $glowColorClass }} w-11 h-11 rounded">
                                        {{-- Используем правильный маршрут --}}
                                        <a href="{{ route('user.equipped-item.details', ['gameItem' => $gameItem->id]) }}"
                                            class="block w-full h-full">
                                            <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                                alt="{{ $item->name ?? 'Предмет' }}"
                                                class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110 rounded" />
                                        </a>
                                    </div>
                                    {{-- ИЗМЕНЕНО: Всплывающая подсказка удалена --}}
                                @else
                                    {{-- Если предмет есть, но связь item отсутствует --}}
                                    <img src="{{ asset('assets/armorIcon.png') }}" alt="Ошибка загрузки предмета"
                                        class="w-11 h-11 object-cover opacity-50" />
                                @endif
                            @else
                                <img src="{{ asset('assets/armorIcon.png') }}" alt="Пустой слот броня"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 right-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                        {{-- Слот перчаток (аналогично) --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            @if (isset($equippedItems['перчатки']) && $equippedItems['перчатки']->item)
                                @php
                                    $gameItem = $equippedItems['перчатки'];
                                    $item = $gameItem->item;
                                    
                                    // Русский комментарий: Определение цвета качества предмета для свечения
                                    $qualityColors = [
                                        'Обычное' => 'glow-color-gray-200',
                                        'Необычное' => 'glow-color-green-400',
                                        'Редкое' => 'glow-color-blue-400',
                                        'Эпическое' => 'glow-color-purple-400',
                                        'Легендарное' => 'glow-color-orange-400',
                                        'Мифическое' => 'glow-color-red-500',
                                        'Божественное' => 'glow-color-pink-400',
                                    ];
                                    $glowColorClass = $qualityColors[$item->quality ?? 'Обычное'] ?? 'glow-color-gray-200';
                                @endphp
                                {{-- Контейнер с анимацией свечения --}}
                                <div class="animate-breathing-glow {{ $glowColorClass }} w-11 h-11 rounded">
                                    {{-- Используем правильный маршрут --}}
                                    <a href="{{ route('user.equipped-item.details', ['gameItem' => $gameItem->id]) }}"
                                        class="block w-full h-full">
                                        <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                            alt="{{ $item->name ?? 'Предмет' }}"
                                            class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110 rounded" />
                                    </a>
                                </div>
                                {{-- ИЗМЕНЕНО: Всплывающая подсказка удалена --}}
                            @else
                                <img src="{{ asset('assets/glovesIcon.png') }}" alt="Пустой слот перчатки"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 right-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                        {{-- Слот обуви (аналогично) --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            @if (isset($equippedItems['обувь']) && $equippedItems['обувь']->item)
                                @php
                                    $gameItem = $equippedItems['обувь'];
                                    $item = $gameItem->item;
                                    
                                    // Русский комментарий: Определение цвета качества предмета для свечения
                                    $qualityColors = [
                                        'Обычное' => 'glow-color-gray-200',
                                        'Необычное' => 'glow-color-green-400',
                                        'Редкое' => 'glow-color-blue-400',
                                        'Эпическое' => 'glow-color-purple-400',
                                        'Легендарное' => 'glow-color-orange-400',
                                        'Мифическое' => 'glow-color-red-500',
                                        'Божественное' => 'glow-color-pink-400',
                                    ];
                                    $glowColorClass = $qualityColors[$item->quality ?? 'Обычное'] ?? 'glow-color-gray-200';
                                @endphp
                                {{-- Контейнер с анимацией свечения --}}
                                <div class="animate-breathing-glow {{ $glowColorClass }} w-11 h-11 rounded">
                                    {{-- Используем правильный маршрут --}}
                                    <a href="{{ route('user.equipped-item.details', ['gameItem' => $gameItem->id]) }}"
                                        class="block w-full h-full">
                                        <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                            alt="{{ $item->name ?? 'Предмет' }}"
                                            class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110 rounded" />
                                    </a>
                                </div>
                                {{-- ИЗМЕНЕНО: Всплывающая подсказка удалена --}}
                            @else
                                <img src="{{ asset('assets/bootsIcon.png') }}" alt="Пустой слот обувь"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 right-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                    </div>

                    {{-- Центральная картинка персонажа (как в profileOther) --}}
                    <div
                        class="w-44 h-64 bg-[#38352c] border-2 border-[#514b3c] rounded-lg flex items-center justify-center shadow-lg relative overflow-hidden">
                        {{-- Декоративные уголки --}}
                        <div
                            class="absolute top-1 left-1 w-4 h-4 border-t-2 border-l-2 border-[#a6925e]/50 rounded-tl-md">
                        </div>
                        <div
                            class="absolute top-1 right-1 w-4 h-4 border-t-2 border-r-2 border-[#a6925e]/50 rounded-tr-md">
                        </div>
                        <div
                            class="absolute bottom-1 left-1 w-4 h-4 border-b-2 border-l-2 border-[#a6925e]/50 rounded-bl-md">
                        </div>
                        <div
                            class="absolute bottom-1 right-1 w-4 h-4 border-b-2 border-r-2 border-[#a6925e]/50 rounded-br-md">
                        </div>

                        {{-- Динамическое изображение персонажа на основе расы --}}
                        @php
                            $avatarPath = 'assets/character-placeholder.png'; // Заглушка по умолчанию
                            if ($userProfile->race === 'solarius') {
                                $avatarPath = 'assets/avatar/hero-solarius.jpg';
                            } elseif ($userProfile->race === 'lunarius') {
                                $avatarPath = 'assets/avatar/hero-lunarius.jpg';
                            }
                        @endphp
                        <img src="{{ asset($avatarPath) }}"
                            alt="Персонаж {{ getRaceDisplayName($userProfile->race ?? 'solarius') }}"
                            class="absolute inset-0 w-full h-full object-cover rounded-lg" />

                        {{-- Кнопка "сменить" под аватаркой --}}
                        <div class="absolute bottom-3 left-1/2 transform -translate-x-1/2 z-10">
                            <span
                                class="text-[#d9d3b8] text-xs opacity-70 cursor-pointer hover:opacity-100 hover:text-[#e5b769] transition-all duration-200 bg-black bg-opacity-50 px-2 py-1 rounded">
                                сменить
                            </span>
                        </div>
                    </div>

                    {{-- Правая колонка слотов --}}
                    <div class="flex flex-col space-y-2.5 ml-1">
                        {{-- Слот браслета --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            @if (isset($equippedItems['браслет']) && $equippedItems['браслет']->item)
                                @php
                                    $gameItem = $equippedItems['браслет'];
                                    $item = $gameItem->item;
                                    
                                    // Русский комментарий: Определение цвета качества предмета для свечения
                                    $qualityColors = [
                                        'Обычное' => 'glow-color-gray-200',
                                        'Необычное' => 'glow-color-green-400',
                                        'Редкое' => 'glow-color-blue-400',
                                        'Эпическое' => 'glow-color-purple-400',
                                        'Легендарное' => 'glow-color-orange-400',
                                        'Мифическое' => 'glow-color-red-500',
                                        'Божественное' => 'glow-color-pink-400',
                                    ];
                                    $glowColorClass = $qualityColors[$item->quality ?? 'Обычное'] ?? 'glow-color-gray-200';
                                @endphp
                                {{-- Контейнер с анимацией свечения --}}
                                <div class="animate-breathing-glow {{ $glowColorClass }} w-11 h-11 rounded">
                                    {{-- Используем правильный маршрут --}}
                                    <a href="{{ route('user.equipped-item.details', ['gameItem' => $gameItem->id]) }}"
                                        class="block w-full h-full">
                                        <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                            alt="{{ $item->name ?? 'Предмет' }}"
                                            class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110 rounded" />
                                    </a>
                                </div>
                                {{-- ИЗМЕНЕНО: Всплывающая подсказка удалена --}}
                            @else
                                <img src="{{ asset('assets/bracerIcon.png') }}" alt="Пустой слот браслет"
                                    class="w-11 h-11 object-cover opacity-30" />
                                {{-- Индикатор пустого слота (+) - слева для правых слотов --}}
                                <div
                                    class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                        {{-- Слот пояса (аналогично) --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            @if (isset($equippedItems['пояс']) && $equippedItems['пояс']->item)
                                @php
                                    $gameItem = $equippedItems['пояс'];
                                    $item = $gameItem->item;
                                    
                                    // Русский комментарий: Определение цвета качества предмета для свечения
                                    $qualityColors = [
                                        'Обычное' => 'glow-color-gray-200',
                                        'Необычное' => 'glow-color-green-400',
                                        'Редкое' => 'glow-color-blue-400',
                                        'Эпическое' => 'glow-color-purple-400',
                                        'Легендарное' => 'glow-color-orange-400',
                                        'Мифическое' => 'glow-color-red-500',
                                        'Божественное' => 'glow-color-pink-400',
                                    ];
                                    $glowColorClass = $qualityColors[$item->quality ?? 'Обычное'] ?? 'glow-color-gray-200';
                                @endphp
                                {{-- Контейнер с анимацией свечения --}}
                                <div class="animate-breathing-glow {{ $glowColorClass }} w-11 h-11 rounded">
                                    {{-- Используем правильный маршрут --}}
                                    <a href="{{ route('user.equipped-item.details', ['gameItem' => $gameItem->id]) }}"
                                        class="block w-full h-full">
                                        <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                            alt="{{ $item->name ?? 'Предмет' }}"
                                            class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110 rounded" />
                                    </a>
                                </div>
                                {{-- ИЗМЕНЕНО: Всплывающая подсказка удалена --}}
                            @else
                                <img src="{{ asset('assets/beltIcon.png') }}" alt="Пустой слот пояс"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                        {{-- Слот оружия (аналогично) --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">


                            @if (isset($equippedItems['оружие']) && $equippedItems['оружие']->item)
                                @php
                                    $gameItem = $equippedItems['оружие'];
                                    $item = $gameItem->item;
                                    
                                    // Русский комментарий: Определение цвета качества предмета для свечения
                                    $qualityColors = [
                                        'Обычное' => 'glow-color-gray-200',
                                        'Необычное' => 'glow-color-green-400',
                                        'Редкое' => 'glow-color-blue-400',
                                        'Эпическое' => 'glow-color-purple-400',
                                        'Легендарное' => 'glow-color-orange-400',
                                        'Мифическое' => 'glow-color-red-500',
                                        'Божественное' => 'glow-color-pink-400',
                                    ];
                                    $glowColorClass = $qualityColors[$item->quality ?? 'Обычное'] ?? 'glow-color-gray-200';
                                @endphp
                                {{-- Контейнер с анимацией свечения --}}
                                <div class="animate-breathing-glow {{ $glowColorClass }} w-11 h-11 rounded">
                                    {{-- Используем правильный маршрут --}}
                                    <a href="{{ route('user.equipped-item.details', ['gameItem' => $gameItem->id]) }}"
                                        class="block w-full h-full">
                                        <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                            alt="{{ $item->name ?? 'Предмет' }}"
                                            class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110 rounded" />
                                    </a>
                                </div>
                                {{-- ИЗМЕНЕНО: Всплывающая подсказка удалена --}}
                            @else
                                <img src="{{ asset('assets/weaponIcon.png') }}" alt="Пустой слот оружие"
                                    class="w-11 h-11 object-cover opacity-45" /> {{-- Немного выше opacity для основного
                                оружия --}}
                                <div
                                    class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                        {{-- Слот доп. оружия (аналогично) --}}
                        <div
                            class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                            @if (isset($equippedItems['доп оружие']) && $equippedItems['доп оружие']->item)
                                @php
                                    $gameItem = $equippedItems['доп оружие'];
                                    $item = $gameItem->item;
                                    
                                    // Русский комментарий: Определение цвета качества предмета для свечения
                                    $qualityColors = [
                                        'Обычное' => 'glow-color-gray-200',
                                        'Необычное' => 'glow-color-green-400',
                                        'Редкое' => 'glow-color-blue-400',
                                        'Эпическое' => 'glow-color-purple-400',
                                        'Легендарное' => 'glow-color-orange-400',
                                        'Мифическое' => 'glow-color-red-500',
                                        'Божественное' => 'glow-color-pink-400',
                                    ];
                                    $glowColorClass = $qualityColors[$item->quality ?? 'Обычное'] ?? 'glow-color-gray-200';
                                @endphp
                                {{-- Контейнер с анимацией свечения --}}
                                <div class="animate-breathing-glow {{ $glowColorClass }} w-11 h-11 rounded">
                                    {{-- Используем правильный маршрут --}}
                                    <a href="{{ route('user.equipped-item.details', ['gameItem' => $gameItem->id]) }}"
                                        class="block w-full h-full">
                                        <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                            alt="{{ $item->name ?? 'Предмет' }}"
                                            class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110 rounded" />
                                    </a>
                                </div>
                                {{-- ИЗМЕНЕНО: Всплывающая подсказка удалена --}}
                            @else
                                <img src="{{ asset('assets/weapon2Icon.png') }}" alt="Пустой слот доп. оружие"
                                    class="w-11 h-11 object-cover opacity-30" />
                                <div
                                    class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                    <span class="text-[#8c784e] text-xs">+</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                {{-- Нижние иконки (кольца и амулет) --}}
                {{-- ИЗМЕНЕНО: Удален mt-4 --}}
                <div class="grid grid-cols-3 gap-3">
                    {{-- Слот кольцо 1 --}}
                    {{-- Стили слота с градиентом, рамкой, тенью, эффектами и декоративной линией --}}
                    <div
                        class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group overflow-hidden transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                        @if (isset($equippedItems['кольцо 1']) && $equippedItems['кольцо 1']->item)
                            @php
                                $gameItem = $equippedItems['кольцо 1'];
                                $item = $gameItem->item;
                                
                                // Русский комментарий: Определение цвета качества предмета для свечения
                                $qualityColors = [
                                    'Обычное' => 'glow-color-gray-200',
                                    'Необычное' => 'glow-color-green-400',
                                    'Редкое' => 'glow-color-blue-400',
                                    'Эпическое' => 'glow-color-purple-400',
                                    'Легендарное' => 'glow-color-orange-400',
                                    'Мифическое' => 'glow-color-red-500',
                                    'Божественное' => 'glow-color-pink-400',
                                ];
                                $glowColorClass = $qualityColors[$item->quality ?? 'Обычное'] ?? 'glow-color-gray-200';
                            @endphp
                            {{-- Контейнер с анимацией свечения --}}
                            <div class="animate-breathing-glow {{ $glowColorClass }} w-11 h-11 rounded">
                                {{-- Ссылка на детали предмета --}}
                                <a href="{{ route('user.equipped-item.details', ['gameItem' => $gameItem->id]) }}"
                                    class="block w-full h-full">
                                    {{-- Иконка предмета с эффектом увеличения --}}
                                    <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                        alt="{{ $item->name ?? 'Предмет' }}"
                                        class="w-full h-full object-cover transform hover:scale-110 transition-transform duration-300 rounded" />
                                </a>
                            </div>
                            {{-- ИЗМЕНЕНО: Всплывающая подсказка удалена --}}
                        @else
                            {{-- Заглушка для пустого слота --}}
                            <img src="{{ asset('assets/ringIcon.png') }}" alt="Пустой слот кольцо 1"
                                class="w-11 h-11 object-cover opacity-30" />
                            {{-- Индикатор пустого слота (+) --}}
                            <div
                                class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                <span class="text-[#8c784e] text-xs">+</span>
                            </div>
                        @endif
                        {{-- Декоративная линия снизу слота --}}
                        <div
                            class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50">
                        </div>
                    </div>
                    {{-- Слот кольцо 2 (аналогично) --}}
                    <div
                        class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group overflow-hidden transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                        @if (isset($equippedItems['кольцо 2']) && $equippedItems['кольцо 2']->item)
                            @php
                                $gameItem = $equippedItems['кольцо 2'];
                                $item = $gameItem->item;
                                
                                // Русский комментарий: Определение цвета качества предмета для свечения
                                $qualityColors = [
                                    'Обычное' => 'glow-color-gray-200',
                                    'Необычное' => 'glow-color-green-400',
                                    'Редкое' => 'glow-color-blue-400',
                                    'Эпическое' => 'glow-color-purple-400',
                                    'Легендарное' => 'glow-color-orange-400',
                                    'Мифическое' => 'glow-color-red-500',
                                    'Божественное' => 'glow-color-pink-400',
                                ];
                                $glowColorClass = $qualityColors[$item->quality ?? 'Обычное'] ?? 'glow-color-gray-200';
                            @endphp
                            {{-- Контейнер с анимацией свечения --}}
                            <div class="animate-breathing-glow {{ $glowColorClass }} w-11 h-11 rounded">
                                {{-- Ссылка на детали предмета --}}
                                <a href="{{ route('user.equipped-item.details', ['gameItem' => $gameItem->id]) }}"
                                    class="block w-full h-full">
                                    <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                        alt="{{ $item->name ?? 'Предмет' }}"
                                        class="w-full h-full object-cover transform hover:scale-110 transition-transform duration-300 rounded" />
                                </a>
                            </div>
                            {{-- ИЗМЕНЕНО: Всплывающая подсказка удалена --}}
                        @else
                            <img src="{{ asset('assets/ringIcon.png') }}" alt="Пустой слот кольцо 2"
                                class="w-11 h-11 object-cover opacity-30" />
                            <div
                                class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                <span class="text-[#8c784e] text-xs">+</span>
                            </div>
                        @endif
                        <div
                            class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50">
                        </div>
                    </div>
                    {{-- Слот амулета (аналогично) --}}
                    <div
                        class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group overflow-hidden transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                        @if (isset($equippedItems['амулет']) && $equippedItems['амулет']->item)
                            @php
                                $gameItem = $equippedItems['амулет'];
                                $item = $gameItem->item;
                                
                                // Русский комментарий: Определение цвета качества предмета для свечения
                                $qualityColors = [
                                    'Обычное' => 'glow-color-gray-200',
                                    'Необычное' => 'glow-color-green-400',
                                    'Редкое' => 'glow-color-blue-400',
                                    'Эпическое' => 'glow-color-purple-400',
                                    'Легендарное' => 'glow-color-orange-400',
                                    'Мифическое' => 'glow-color-red-500',
                                    'Божественное' => 'glow-color-pink-400',
                                ];
                                $glowColorClass = $qualityColors[$item->quality ?? 'Обычное'] ?? 'glow-color-gray-200';
                            @endphp
                            {{-- Контейнер с анимацией свечения --}}
                            <div class="animate-breathing-glow {{ $glowColorClass }} w-11 h-11 rounded">
                                {{-- Ссылка на детали предмета --}}
                                <a href="{{ route('user.equipped-item.details', ['gameItem' => $gameItem->id]) }}"
                                    class="block w-full h-full">
                                    <img src="{{ asset($item->icon ?? 'assets/placeholder.png') }}"
                                        alt="{{ $item->name ?? 'Предмет' }}"
                                        class="w-full h-full object-cover transform hover:scale-110 transition-transform duration-300 rounded" />
                                </a>
                            </div>
                            {{-- ИЗМЕНЕНО: Всплывающая подсказка удалена --}}
                        @else
                            <img src="{{ asset('assets/amuletIcon.png') }}" alt="Пустой слот амулет"
                                class="w-11 h-11 object-cover opacity-30" />
                            <div
                                class="absolute top-0 left-0 w-3 h-3 bg-[#2b2a21] border border-[#8c784e] rounded-full flex items-center justify-center">
                                <span class="text-[#8c784e] text-xs">+</span>
                            </div>
                        @endif
                        <div
                            class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-50">
                        </div>
                    </div>
                </div>

                {{-- Умения персонажа --}}
                {{-- ИЗМЕНЕНО: mt-6 -> mt-5, px-5 -> px-3, grid-cols-3 gap-3 p-4 -> grid-cols-4 gap-5 p-3, добавлено 4
                слота --}}
                <div class="mt-5 w-full">
                    {{-- Заголовок "Умения" в овале --}}
                    <div class="relative mb-3">
                        <div
                            class="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-[#2a271c] px-3 py-1 border border-[#a6925e] rounded-full z-10 shadow-md">
                            <h3 class="text-[#e5b769] text-base font-semibold">Умения</h3> {{-- Заголовок секции умений
                            --}}
                        </div>
                    </div>
                    {{-- Контейнер для слотов умений --}}
                    <div
                        class="grid grid-cols-4 gap-5 bg-[#2a271c] border border-[#514b3c] rounded-lg p-3 shadow-lg mt-2 mx-auto max-w-xs">
                        {{-- Отображаем 4 слота для умений (пока заглушки) --}}
                        @for ($i = 0; $i < 4; $i++)
                                {{-- ИЗМЕНЕНО: $i < 3 -> $i < 4 --}} {{-- Стили слота умения --}} <div
                                        class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg shadow-inner relative group overflow-hidden transition-all duration-200 hover:shadow-lg hover:shadow-[#a6925e]/30">
                                        @if ($i == 0)
                                            {{-- Первый слот с иконкой Fireball --}}
                                            <img src="{{ asset('assets/skillFireball.png') }}" alt="Огненный шар"
                                                class="w-11 h-11 object-cover transition-transform duration-200 group-hover:scale-110" />
                                        @else
                                            {{-- Остальные слоты с заглушкой --}}
                                            <div
                                                class="w-11 h-11 bg-gradient-to-br from-[#4a4a3d] to-[#3b3a33] flex items-center justify-center rounded-md">
                                                <span class="text-[#8c784e] text-2xl">?</span> {{-- Знак вопроса как заглушка --}}
                                            </div>
                                        @endif
                                        {{-- Декоративная линия снизу --}}
                                        <div
                                            class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-30">
                                        </div>
                            </div>
                        @endfor
                </div>
            </div>

            {{-- Панель быстрого доступа --}}
            {{-- ИЗМЕНЕНО: space-x-5 mt-6 mb-3 -> space-x-3 mt-2.5, удалена кнопка настроек --}}
            <div class="flex items-center justify-center space-x-3 mt-2.5">
                {{-- Кнопка "Умения" --}}
                <button
                    class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg transition-all duration-300 group relative hover:shadow-lg hover:shadow-[#a6925e]/30">
                    {{-- Иконка умений --}}
                    <img src="{{ asset('assets/iconBook.png') }}" alt="умение"
                        class="w-9 h-9 transform group-hover:scale-110 transition-transform duration-300">
                </button>
                {{-- Кнопка "Снаряжение" (ссылка на свое снаряжение) --}}
                <a href="{{ route('user.equipment') }}"
                    class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg transition-all duration-300 group relative hover:shadow-lg hover:shadow-[#a6925e]/30">
                    {{-- Иконка снаряжения --}}
                    <img src="{{ asset('assets/test1.png') }}" alt="снаряжение"
                        class="w-9 h-9 transform group-hover:scale-110 transition-transform duration-300">
                </a>
                {{-- Кнопка "Почта" --}}
                <a href="{{ route('messages.index') }}"
                    class="w-14 h-14 bg-gradient-to-b from-[#3b3a33] to-[#2b2a21] border-2 border-[#514b3c] hover:border-[#e5b769] rounded-md flex items-center justify-center shadow-lg transition-all duration-300 group relative hover:shadow-lg hover:shadow-[#a6925e]/30">
                    {{-- Иконка почты --}}
                    @if($hasUnreadMessages)
                        <img src="{{ asset('assets/notification/message.png') }}" alt="Сообщения"
                            class="w-9 h-9 animate-pulse filter hover:brightness-125 transition-all duration-300 transform group-hover:scale-110"
                            style="animation: messagePulse 2s infinite;">
                    @else
                        <span class="text-[#e5b769] text-2xl">📩</span>
                    @endif
                    {{-- Индикатор непрочитанных сообщений --}}
                    @if($hasUnreadMessages)
                        <div
                            class="absolute -top-2 -right-2 bg-[#e74c3c] text-white text-xs rounded-full w-5 h-5 flex items-center justify-center animate-pulse">
                            {{ $unreadMessagesCount > 9 ? '9+' : $unreadMessagesCount }}
                        </div>
                    @endif
                </a>
            </div>
    </div>

    {{-- ИЗМЕНЕНО: Разделитель удален --}}

    {{-- Характеристики персонажа --}}
    {{-- ИЗМЕНЕНО: Удален внешний фон/рамка/паддинг, удален главный заголовок, изменены паддинги/отступы/иконки
    подсекций --}}
    <div class="rounded-lg mb-6 shadow-lg relative overflow-hidden"> {{-- Удалены классы bg, border, p --}}
        {{-- ИЗМЕНЕНО: Заголовок блока характеристик удален --}}
        {{-- Одна колонка для всех характеристик --}}
        <div class="grid grid-cols-1 gap-3">
            {{-- ОСНОВНЫЕ ХАРАКТЕРИСТИКИ --}}
            <div class="bg-[#2a271c] bg-opacity-90 rounded-lg p-3 border border-[#3d3828]">
                {{-- p-3 как в profileOther --}}
                {{-- Заголовок подраздела "Основные" --}}
                {{-- Заголовок удален, т.к. его нет в profileOther в этом месте --}}
                <div class="space-y-1"> {{-- ИЗМЕНЕНО: space-y-2.5 -> space-y-1 --}}
                    {{-- Сила --}}
                    <div class="flex items-center">
                        {{-- ИЗМЕНЕНО: Иконка силы удалена --}}
                        {{-- Название и значение --}}
                        <div class="flex-1"><span class="text-base text-[#d9d3b8]">Сила: </span><span
                                class="text-base text-[#ffc83d] font-medium">{{ $effectiveStats['strength'] ?? 0 }}</span>
                        </div>
                    </div>
                    {{-- Интеллект --}}
                    <div class="flex items-center">
                        {{-- ИЗМЕНЕНО: Иконка интеллекта удалена --}}
                        <div class="flex-1"><span class="text-base text-[#d9d3b8]">Интеллект: </span><span
                                class="text-base text-[#ffc83d] font-medium">{{ $effectiveStats['intelligence'] ?? 0 }}</span>
                        </div>
                    </div>
                    {{-- Броня --}}
                    <div class="flex items-center">
                        {{-- ИЗМЕНЕНО: Иконка брони удалена --}}
                        <div class="flex-1">
                            <span class="text-base text-[#d9d3b8]">Броня: </span>
                            <span
                                class="text-base text-[#ffc83d] font-medium">{{ round($effectiveStats['armor'] ?? 0) }}</span>
                            {{-- Процент снижения урона от брони (серым цветом в скобках) --}}
                            <span
                                class="text-base text-gray-400 ml-1">({{ $userProfile->getFormattedArmorReduction() }})</span>
                        </div>
                    </div>
                    {{-- Восстановление --}}
                    <div class="flex items-center">
                        {{-- ИЗМЕНЕНО: Иконка восстановления удалена --}}
                        <div class="flex-1"><span class="text-base text-[#d9d3b8]">Восстановление:
                            </span><span
                                class="text-base text-[#ffc83d] font-medium">{{ $effectiveStats['recovery'] ?? 0 }}</span>
                        </div>
                    </div>
                    {{-- Шанс крита --}}
                    <div class="flex items-center">
                        {{-- ИЗМЕНЕНО: Иконка шанса крита удалена --}}
                        <div class="flex-1">
                            <span class="text-base text-[#d9d3b8]">Шанс крита: </span>
                            <span
                                class="text-base text-[#ffc83d] font-medium">{{ $effectiveStats['crit_chance'] ?? 0 }}</span>
                            {{-- Форматированное значение уже отображается корректно --}}
                            @if ($userProfile->getFormattedCritChance() > 0)
                                <span
                                    class="text-base text-green-600 ">({{ $userProfile->getFormattedCritChance() }})</span>
                                {{-- ИЗМЕНЕНО: text-xs -> text-base, font-semibold убран --}}
                            @endif
                        </div>
                    </div>
                    {{-- Сила крита --}}
                    <div class="flex items-center">
                        {{-- ИЗМЕНЕНО: Иконка силы крита удалена --}}
                        <div class="flex-1">
                            <span class="text-base text-[#d9d3b8]">Сила крита: </span>
                            <span
                                class="text-base text-[#ffc83d] font-medium">{{ $effectiveStats['crit_damage'] ?? 0 }}</span>
                            {{-- Форматированное значение уже отображается корректно --}}
                            @if ($userProfile->getFormattedCritDamage() > 0)
                                <span
                                    class="text-base text-green-600 ">({{ $userProfile->getFormattedCritDamage() }})</span>
                                {{-- ИЗМЕНЕНО: text-xs -> text-base, font-semibold убран --}}
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            {{-- РЕСУРСЫ (HP/MP) и АКТИВНЫЕ ЭФФЕКТЫ --}}
            <div class="bg-[#2a271c] bg-opacity-90 rounded-lg p-1.5 border border-[#3d3828]">
                {{-- ИЗМЕНЕНО: p-3 -> p-1.5 --}}
                {{-- Заголовок подраздела "Ресурсы" --}}
                {{-- Заголовок удален --}}
                <div class="space-y-1"> {{-- ИЗМЕНЕНО: space-y-2.5 -> space-y-1 --}}
                    {{-- HP --}}
                    <div class="flex items-center">
                        {{-- Иконка HP --}}
                        <img src="{{ asset('assets/user/hpIcon.png') }}" alt="HP" class="w-6 h-6 mr-2.5">
                        {{-- Текстовое значение HP (актуальное/максимальное) + регенерация --}}
                        <div class="flex-1">
                            {{-- Русский комментарий: выводим актуальное и максимальное HP, а также скорость
                            регенерации HP/сек. Значения берутся из контроллера, а ниже JS обновляет их каждую
                            секунду на клиенте. --}}
                            <span class="text-base text-[#d9d3b8]">HP: </span>
                            <span id="hp-value" class="text-base text-[#ffc83d] font-medium"
                                data-hp="{{ $actualResources['current_hp'] ?? 0 }}"
                                data-max-hp="{{ $userProfile->max_hp ?? 0 }}"
                                data-hp-regen="{{ isset($regenerationRates['hp_per_second']) ? (float) $regenerationRates['hp_per_second'] : 0 }}">
                                {{ $actualResources['current_hp'] ?? 0 }} / {{ $userProfile->max_hp ?? '?' }}
                                ({{ isset($regenerationRates['hp_per_second']) ? (int) $regenerationRates['hp_per_second'] : 0 }}/сек)
                            </span>
                        </div>
                    </div>
                    {{-- MP --}}
                    <div class="flex items-center">
                        {{-- Иконка MP --}}
                        <img src="{{ asset('assets/user/mp-icon.png') }}" alt="MP" class="w-6 h-6 mr-2.5">
                        {{-- Текстовое значение MP (актуальное/максимальное) + регенерация --}}
                        <div class="flex-1">
                            {{-- Русский комментарий: выводим актуальное и максимальное MP, а также скорость
                            регенерации MP/сек. Значения берутся из контроллера, а ниже JS обновляет их каждую
                            секунду на клиенте. --}}
                            <span class="text-base text-[#d9d3b8]">MP: </span>
                            <span id="mp-value" class="text-base text-[#ffc83d] font-medium"
                                data-mp="{{ $actualResources['current_mp'] ?? 0 }}"
                                data-max-mp="{{ $userProfile->max_mp ?? 0 }}"
                                data-mp-regen="{{ isset($regenerationRates['mp_per_second']) ? (float) $regenerationRates['mp_per_second'] : 0 }}">
                                {{ $actualResources['current_mp'] ?? 0 }} / {{ $userProfile->max_mp ?? '?' }}
                                ({{ isset($regenerationRates['mp_per_second']) ? (int) $regenerationRates['mp_per_second'] : 0 }}/сек)
                            </span>
                        </div>
                    </div>
                </div>

                {{-- Блок Боевая Активность --}}
                <div class="flex justify-between items-center text-[#d9d3b8] rounded-md pt-1.5 shadow-inner mt-2">
                    {{-- Иконка и текст Боевая Активность --}}
                    <div class="flex items-center">
                        <div
                            class="w-6 h-6 flex items-center justify-center bg-[#38352c] rounded-full border border-[#a6925e] mr-1">
                            {{-- Иконка боевой активности --}}
                            <span class="text-[#e74c3c] text-xs">⚔️</span>
                        </div>
                        <div class="flex flex-col">
                            {{-- Текстовое значение Боевой Активности --}}
                            <span class="text-[#e5b769] text-base">Боевая Активность:
                                {{ number_format($combatActivity->damage_dealt ?? 0) }}</span>
                        </div>
                    </div>
                </div>
                {{-- Активные эффекты --}}
                {{-- ИЗМЕНЕНО: mt-5 -> mt-2, убрано mb-2 у заголовка --}}
                <div class="mt-2">
                    {{-- Заголовок "Активные эффекты" --}}
                    <div class="text-[#d9d3b8] text-base font-medium">Активные эффекты</div>
                    {{-- Контейнер для иконок эффектов --}}
                    <div class="flex flex-wrap gap-2.5 p-2.5 bg-[#191815] rounded border border-[#3d3828]">
                        @if($currentlyActiveEffects->count() > 0)
                            {{-- Русский комментарий: Отображаем реальные активные эффекты игрока --}}
                            @foreach($currentlyActiveEffects as $effect)
                                <div class="flex flex-col items-center text-center"
                                    title="{{ $effect->skill->name ?? 'Неизвестный эффект' }} ({{ $effect->remaining_duration }} сек.)">
                                    <div class="relative">
                                        <div
                                            class="w-9 h-9 rounded-full bg-[#222114] border border-[#a6925e] flex items-center justify-center overflow-hidden">
                                            {{-- Русский комментарий: Используем иконку эффекта или заглушку --}}
                                            @php
                                                // Получаем путь к иконке эффекта или используем заглушку
                                                $effectIcon = $effect->skill->icon_path ?? asset('assets/skillLightOfFaith.png');
                                            @endphp
                                            <img src="{{ $effectIcon }}"
                                                alt="{{ $effect->skill->name ?? 'Неизвестный эффект' }}"
                                                class="w-7 h-7 object-cover rounded-full">
                                        </div>
                                        @if($effect->stacks > 1)
                                            <div
                                                class="absolute -top-1 -right-1 w-4 h-4 bg-[#222114] rounded-full flex items-center justify-center border border-[#a6925e]">
                                                <span class="text-[#e5b769] text-[9px]">{{ $effect->stacks }}</span>
                                            </div>
                                        @endif
                                    </div>
                                    {{-- Русский комментарий: Отображаем время в секундах под иконкой --}}
                                    <span class="text-[#e5b769] text-xs mt-1 font-medium">
                                        {{ $effect->remaining_duration }}с
                                    </span>
                                </div>
                            @endforeach
                        @else
                            {{-- Русский комментарий: Заглушка когда нет активных эффектов --}}
                            <div class="w-full text-center text-[#8c784e] text-sm py-2">
                                Нет активных эффектов
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            {{-- СОПРОТИВЛЕНИЯ --}}
            <!-- <div class="bg-[#2a271c] bg-opacity-90 rounded-lg p-1.5 border border-[#3d3828]"> {{-- ИЗМЕНЕНО: p-3 -> p-1.5 --}}
                        {{-- Заголовок подраздела "Сопротивления" --}}
                        {{-- Заголовок удален --}}
                        <div class="space-y-1"> {{-- ИЗМЕНЕНО: space-y-2.5 -> space-y-1 --}}
                             {{-- Сопротивление огню --}}
                             <div class="flex items-center">
                                 {{-- ИЗМЕНЕНО: Иконка огня удалена --}}
                                {{-- Название и значение --}}
                                <div class="flex-1"><span class="text-base text-[#d9d3b8]">Огонь: </span><span class="text-base text-[#ff7b4a] font-medium">{{ $effectiveStats['resistance_fire'] ?? 0 }}%</span></div>
                            </div>
                             {{-- Сопротивление молнии --}}
                            <div class="flex items-center">
                                {{-- ИЗМЕНЕНО: Иконка молнии удалена --}}
                                {{-- Название и значение --}}
                                <div class="flex-1"><span class="text-base text-[#d9d3b8]">Молния: </span><span class="text-base text-[#4aecff] font-medium">{{ $effectiveStats['resistance_lightning'] ?? 0 }}%</span></div>
                            </div>
                            {{-- ИЗМЕНЕНО: Другие примеры сопротивлений удалены --}}
                        </div>
                    </div> -->
        </div>
    </div>

    {{-- ИЗМЕНЕНО: Разделитель удален --}}

    {{-- Статистика Боев --}}
    {{-- ИЗМЕНЕНО: mb-6 удален, удален главный заголовок --}}
    <div class="bg-[#211f1a] border border-[#a6925e] rounded-lg p-4 shadow-lg relative overflow-hidden">
        {{-- Удален mb-6 --}}
        {{-- Верхняя декоративная линия --}}
        <div
            class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-50">
        </div>
        {{-- ИЗМЕНЕНО: Заголовок блока удален --}}
        {{-- Сетка для PvP и PvE статистики --}}
        <div class="grid grid-cols-2 gap-4">
            {{-- PvP статистика --}}
            <div class="bg-[#2b2a21] border border-[#514b3c] rounded-lg p-3.5 shadow-sm">
                {{-- Внутренняя карточка --}}
                <div class="text-center font-semibold text-[#e5b769] mb-2 text-base">PvP</div>
                {{-- Заголовок PvP --}}
                {{-- Победы PvP --}}
                <div class="flex justify-between items-center text-[#d9d3b8] text-sm mb-1">
                    <span>Победы:</span>
                    {{-- Используем данные из $userStatistics --}}
                    <span class="text-[#a6e55e] font-medium">{{ $userStatistics->pvp_wins ?? 0 }}</span>
                </div>
                {{-- Поражения PvP --}}
                <div class="flex justify-between items-center text-[#d9d3b8] text-sm">
                    <span>Поражения:</span>
                    {{-- Используем данные из $userStatistics --}}
                    <span class="text-[#e55e5e] font-medium">{{ $userStatistics->pvp_losses ?? 0 }}</span>
                </div>
            </div>
            {{-- PvE статистика --}}
            <div class="bg-[#2b2a21] border border-[#514b3c] rounded-lg p-3.5 shadow-sm">
                {{-- Внутренняя карточка --}}
                <div class="text-center font-semibold text-[#e5b769] mb-2 text-base">PvE</div>
                {{-- Заголовок PvE --}}
                {{-- Победы PvE --}}
                <div class="flex justify-between items-center text-[#d9d3b8] text-sm mb-1">
                    <span>Победы:</span>
                    {{-- Используем данные из $userStatistics --}}
                    <span class="text-[#a6e55e] font-medium">{{ $userStatistics->pve_wins ?? 0 }}</span>
                </div>
                {{-- Поражения PvE --}}
                <div class="flex justify-between items-center text-[#d9d3b8] text-sm">
                    <span>Поражения:</span>
                    {{-- Используем данные из $userStatistics --}}
                    <span class="text-[#e55e5e] font-medium">{{ $userStatistics->pve_losses ?? 0 }}</span>
                </div>
            </div>
        </div>
        {{-- Нижняя декоративная линия --}}
        <div
            class="absolute bottom-0 right-0 w-full h-1 bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-50">
        </div>
    </div>

    {{-- ИЗМЕНЕНО: Разделитель удален --}}

    {{-- Общая информация --}}
    {{-- ИЗМЕНЕНО: Удален главный заголовок, удален блок опыта, удалены отступы/иконки у строк --}}
    <div class="bg-[#211f1a] border border-[#a6925e] rounded-lg p-4 shadow-lg relative overflow-hidden mb-4">
        {{-- Верхняя декоративная линия --}}
        <div
            class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-50">
        </div>
        {{-- ИЗМЕНЕНО: Заголовок блока удален --}}

        {{-- ИЗМЕНЕНО: Шкала опыта удалена --}}

        {{-- Остальная информация --}}
        <div class="text-[#d9d3b8] text-base"> {{-- ИЗМЕНЕНО: Удален класс space-y-3 --}}

            {{-- Статус онлайн/оффлайн --}}
            {{-- ИЗМЕНЕНО: Точке добавлен отрицательный отступ -ml-4 и увеличен правый отступ mr-2 для компенсации
            --}}
            <div class="flex items-center"> {{-- Убедимся, что нет relative --}}
                {{-- Индикатор статуса (точка) --}}
                {{-- Смещен влево на величину p-4 блока, добавлен правый отступ --}}
                <span class="-ml-3 mr-0.5 leading-none {{-- ИЗМЕНЕНО: Добавлен -ml-4, изменен mr-1.5 на mr-2 --}}
                            {{-- Определяем цвет точки по статусу пользователя --}}
                            @if ($userStatus === 'online') text-green-500 animate-pulse {{-- Зеленая точка для онлайна --}}
                            @elseif ($userStatus === 'idle') text-gray-500 {{-- Серая точка для неактивных --}}
                            @else text-red-500 {{-- Красная точка для оффлайна --}} @endif
                        ">●</span>
                {{-- Контейнер для текста статуса (выровнен с остальными строками) --}}
                {{-- Просто выводим $displayLocation, подготовленную контроллером --}}
                <div class="font-medium">
                    {{-- Определяем цвет текста по статусу пользователя --}}
                    @if ($userStatus === 'online')
                        <span class="text-green-400">{{ $displayLocation ?? 'Онлайн (Неизвестно)' }}</span>
                    @elseif ($userStatus === 'idle')
                        <span class="text-gray-400">{{ $displayLocation ?? 'Неактивен' }}</span>
                    @else
                        <span class="text-red-500">{{ $displayLocation ?? 'Не в сети (неизвестно)' }}</span>
                    @endif
                </div>
            </div>

            {{-- Профессия --}}
            <div class="flex items-center"> {{-- ИЗМЕНЕНО: Удален класс space-x-2.5 --}}
                {{-- ИЗМЕНЕНО: Иконка профессии удалена --}}
                {{-- Название профессии --}}
                <div class="font-medium">Профессия: <span
                        class="text-[#e5b769]">{{ $userProfile->profession ?? 'Нет' }}</span></div>
            </div>

            {{-- Время в игре --}}
            <div class="flex items-center"> {{-- ИЗМЕНЕНО: Удален класс space-x-2.5 --}}
                {{-- ИЗМЕНЕНО: Иконка времени удалена --}}
                <div class="font-medium">
                    Провел времени: <span class="text-[#e5b769]">
                        {{-- Форматированное время в игре (часы, минуты) --}}
                        {{ $userStatistics->hours_played ?? 0 }}ч
                        {{ $userStatistics->minutes_played ?? 0 }}м
                        {{-- Можно добавить секунды, если нужно: $userStatistics->seconds_accumulated ?? 0 }}с --}}
                    </span>
                </div>
            </div>
            {{-- Дата регистрации --}}
            <div class="flex items-center"> {{-- ИЗМЕНЕНО: Удален класс space-x-2.5 --}}
                {{-- ИЗМЕНЕНО: Иконка календаря удалена --}}
                <div class="font-medium">
                    В игре с: <span class="text-[#e5b769]">
                        {{-- Форматированная дата регистрации --}}
                        {{ $user->formatted_date ?? $user->created_at->format('d.m.Y') }}
                    </span>
                </div>
            </div>
        </div>
        {{-- Нижняя декоративная линия --}}
        <div
            class="absolute bottom-0 right-0 w-full h-1 bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-50">
        </div>
    </div>
    </x-layout.content-block>
    </div>

    {{-- Нижние кнопки навигации (используем компонент) --}}
    <x-layout.navigation-buttons />

    {{-- Футер (используем компонент) --}}
    <x-layout.footer :onlineCount="$onlineCount" />
</body>

</html>